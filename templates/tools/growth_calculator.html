{% extends "base.html" %}

{% block title %}生长发育计算器 - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.tool-header {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.tool-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.calculator-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #4ecdc4;
    box-shadow: 0 0 0 0.2rem rgba(78, 205, 196, 0.25);
}

.btn-calculate {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    transition: transform 0.3s ease;
    width: 100%;
}

.btn-calculate:hover {
    transform: translateY(-2px);
    color: white;
}

.result-card {
    background: linear-gradient(135deg, #e8f8f5, #f0fff4);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    display: none;
}

.percentile-result {
    text-align: center;
    padding: 1.5rem;
    margin: 1rem 0;
    border-radius: 10px;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.percentile-value {
    font-size: 2rem;
    font-weight: bold;
    color: #4ecdc4;
}

.percentile-label {
    color: #666;
    margin-top: 0.5rem;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="tool-header">
    <div class="container">
        <h1 class="display-5 mb-3">生长发育计算器</h1>
        <p class="lead">根据WHO标准评估宝宝的身高、体重和头围</p>
    </div>
</div>

<div class="container tool-container">
    <div class="calculator-card">
        <form id="growthForm">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">宝宝性别</label>
                    <select class="form-control" id="gender" required>
                        <option value="">请选择</option>
                        <option value="boy">男孩</option>
                        <option value="girl">女孩</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="birthDate" class="form-label">出生日期</label>
                    <input type="date" class="form-control" id="birthDate" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="measureDate" class="form-label">测量日期</label>
                <input type="date" class="form-control" id="measureDate" required>
                <small class="form-text text-muted">测量日期必须在出生日期之后且宝宝不超过2岁</small>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="weight" class="form-label">体重（千克）</label>
                    <input type="number" class="form-control" id="weight" step="0.1" min="1" max="30" required>
                </div>
                <div class="form-group">
                    <label for="height" class="form-label">身高（厘米）</label>
                    <input type="number" class="form-control" id="height" step="0.1" min="30" max="120" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="headCircumference" class="form-label">头围（厘米）</label>
                <input type="number" class="form-control" id="headCircumference" step="0.1" min="25" max="60">
                <small class="form-text text-muted">头围测量是可选的</small>
            </div>
            
            <button type="submit" class="btn btn-calculate">
                <i class="fas fa-chart-line me-2"></i>计算生长百分位
            </button>
        </form>
        
        <div class="result-card" id="resultCard">
            <h3 class="text-center mb-4">生长发育评估结果</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="percentile-result">
                        <div class="percentile-value" id="weightPercentile">-</div>
                        <div class="percentile-label">体重百分位</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="percentile-result">
                        <div class="percentile-value" id="heightPercentile">-</div>
                        <div class="percentile-label">身高百分位</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="percentile-result">
                        <div class="percentile-value" id="headPercentile">-</div>
                        <div class="percentile-label">头围百分位</div>
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <h5>评估说明：</h5>
                <div id="assessment" class="alert alert-info">
                    请输入数据进行计算
                </div>
            </div>
        </div>
    </div>
    
    <div class="info-section">
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> 重要提示</h5>
            <ul class="mb-0">
                <li>生长曲线帮助追踪宝宝的发育情况，但正常范围很广</li>
                <li>第3-97百分位都属于正常范围</li>
                <li>重要的是保持稳定健康的生长趋势</li>
                <li>如有疑虑，请咨询儿科医生</li>
            </ul>
        </div>
    </div>
    
    <div class="text-center mt-4">
        <a href="{{ url_for('tools.tools_index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>返回工具首页
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('growthForm');
    const resultCard = document.getElementById('resultCard');
    
    // 设置默认日期
    const today = new Date();
    document.getElementById('measureDate').value = today.toISOString().split('T')[0];
    
    const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 6, today.getDate());
    document.getElementById('birthDate').value = sixMonthsAgo.toISOString().split('T')[0];
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 获取表单数据
        const formData = {
            gender: document.getElementById('gender').value,
            birthDate: document.getElementById('birthDate').value,
            measureDate: document.getElementById('measureDate').value,
            weight: parseFloat(document.getElementById('weight').value),
            height: parseFloat(document.getElementById('height').value),
            headCircumference: parseFloat(document.getElementById('headCircumference').value) || null
        };
        
        // 验证数据
        if (!formData.gender || !formData.birthDate || !formData.measureDate || !formData.weight || !formData.height) {
            alert('请填写所有必填项');
            return;
        }
        
        // 计算年龄（月数）
        const birthDate = new Date(formData.birthDate);
        const measureDate = new Date(formData.measureDate);
        const ageInMonths = (measureDate - birthDate) / (1000 * 60 * 60 * 24 * 30.44);
        
        if (ageInMonths < 0 || ageInMonths > 24) {
            alert('宝宝年龄必须在0-24个月之间');
            return;
        }
        
        // 简化的百分位计算（实际应用中需要使用WHO标准数据）
        const weightPercentile = calculateSimplePercentile(formData.weight, ageInMonths, 'weight', formData.gender);
        const heightPercentile = calculateSimplePercentile(formData.height, ageInMonths, 'height', formData.gender);
        const headPercentile = formData.headCircumference ? 
            calculateSimplePercentile(formData.headCircumference, ageInMonths, 'head', formData.gender) : null;
        
        displayResults(weightPercentile, heightPercentile, headPercentile);
    });
    
    function calculateSimplePercentile(value, ageInMonths, type, gender) {
        // 这是一个简化的计算，实际应用中需要使用WHO的完整数据表
        // 这里只是为了演示功能
        let baseValue, stdDev;
        
        if (type === 'weight') {
            baseValue = gender === 'boy' ? 3.3 + ageInMonths * 0.6 : 3.2 + ageInMonths * 0.55;
            stdDev = 1.2;
        } else if (type === 'height') {
            baseValue = gender === 'boy' ? 50 + ageInMonths * 2.5 : 49.5 + ageInMonths * 2.4;
            stdDev = 3.5;
        } else if (type === 'head') {
            baseValue = gender === 'boy' ? 35 + ageInMonths * 0.8 : 34.5 + ageInMonths * 0.75;
            stdDev = 1.5;
        }
        
        const zScore = (value - baseValue) / stdDev;
        const percentile = Math.round(normalCDF(zScore) * 100);
        
        return Math.max(1, Math.min(99, percentile));
    }
    
    function normalCDF(x) {
        // 标准正态分布的累积分布函数近似
        return 0.5 * (1 + erf(x / Math.sqrt(2)));
    }
    
    function erf(x) {
        // 误差函数的近似计算
        const a1 =  0.254829592;
        const a2 = -0.284496736;
        const a3 =  1.421413741;
        const a4 = -1.453152027;
        const a5 =  1.061405429;
        const p  =  0.3275911;
        
        const sign = x >= 0 ? 1 : -1;
        x = Math.abs(x);
        
        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
        
        return sign * y;
    }
    
    function displayResults(weightPercentile, heightPercentile, headPercentile) {
        document.getElementById('weightPercentile').textContent = weightPercentile + '%';
        document.getElementById('heightPercentile').textContent = heightPercentile + '%';
        document.getElementById('headPercentile').textContent = headPercentile ? headPercentile + '%' : '-';
        
        // 生成评估说明
        let assessment = '';
        const percentiles = [weightPercentile, heightPercentile];
        if (headPercentile) percentiles.push(headPercentile);
        
        const avgPercentile = percentiles.reduce((a, b) => a + b, 0) / percentiles.length;
        
        if (avgPercentile >= 85) {
            assessment = '宝宝的生长发育指标较高，属于正常范围内的较大体型。';
        } else if (avgPercentile >= 15) {
            assessment = '宝宝的生长发育指标在正常范围内，发育良好。';
        } else {
            assessment = '宝宝的生长发育指标偏低，建议咨询儿科医生。';
        }
        
        document.getElementById('assessment').textContent = assessment;
        resultCard.style.display = 'block';
        resultCard.scrollIntoView({ behavior: 'smooth' });
    }
});
</script>
{% endblock %}
