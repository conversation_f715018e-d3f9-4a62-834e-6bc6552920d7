{% extends "base.html" %}

{% block title %}BabyJourney - 您的孕育旅程好伙伴{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1>陪伴您的每一个孕育时刻</h1>
            <p>专业的孕期指导，贴心的育儿建议，让您的孕育之旅更轻松、更美好</p>
            <div class="hero-buttons">
                <a href="{{ url_for('main.tools') }}" class="btn btn-primary btn-lg">实用工具</a>
                <a href="#featured" class="btn btn-outline-primary btn-lg">精选文章</a>
            </div>
        </div>
        <div class="hero-image">
            <img src="{{ url_for('static', filename='img/hero-image.svg') }}" alt="孕育旅程">
        </div>
    </div>
</section>

<!-- Featured Articles -->
<section id="featured" class="featured-articles">
    <div class="container">
        <h2 class="section-title">精选文章</h2>
        <div class="articles-grid">
            {% for article in featured_articles %}
            <div class="article-card featured">
                <div class="article-image">
                    <img src="{{ article.featured_image }}" alt="{{ article.title }}">
                    {% if article.reviewed %}
                    <div class="medical-badge">
                        <i class="fas fa-check-circle"></i> 医学认证
                    </div>
                    {% endif %}
                </div>
                <div class="article-content">
                    <div class="article-category">{{ article.category.name }}</div>
                    <h3><a href="{{ url_for('main.article_detail', slug=article.slug) }}">{{ article.title }}</a></h3>
                    <p>{{ article.summary }}</p>
                    <div class="article-meta">
                        <span><i class="far fa-eye"></i> {{ article.view_count }}</span>
                        <span><i class="far fa-heart"></i> {{ article.like_count }}</span>
                        <span><i class="far fa-comment"></i> {{ article.comment_count }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Latest Articles -->
<section class="latest-articles">
    <div class="container">
        <h2 class="section-title">最新文章</h2>
        <div class="articles-grid">
            {% for article in latest_articles %}
            <div class="article-card">
                <div class="article-image">
                    <img src="{{ article.featured_image }}" alt="{{ article.title }}">
                </div>
                <div class="article-content">
                    <div class="article-category">{{ article.category.name }}</div>
                    <h3><a href="{{ url_for('main.article_detail', slug=article.slug) }}">{{ article.title }}</a></h3>
                    <p>{{ article.summary }}</p>
                    <div class="article-meta">
                        <span><i class="far fa-calendar"></i> {{ article.published_at.strftime('%Y-%m-%d') }}</span>
                        <span><i class="far fa-heart"></i> {{ article.like_count }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <a href="{{ url_for('main.articles') }}" class="btn btn-outline-primary">查看更多文章</a>
        </div>
    </div>
</section>

<!-- Expert Section -->
<section class="expert-section">
    <div class="container">
        <h2 class="section-title">专业医疗团队</h2>
        <div class="experts-grid">
            {% for expert in experts %}
            <div class="expert-card">
                <div class="expert-image">
                    <img src="{{ expert.avatar }}" alt="{{ expert.name }}">
                </div>
                <div class="expert-content">
                    <h3>{{ expert.name }}</h3>
                    <p class="expert-title">{{ expert.title }}</p>
                    <p class="expert-description">{{ expert.description }}</p>
                    <div class="expert-stats">
                        <span><i class="fas fa-file-medical"></i> {{ expert.articles.count() }} 篇文章</span>
                        <span><i class="fas fa-star"></i> {{ expert.rating }} 评分</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Tools Preview -->
<section class="tools-preview">
    <div class="container">
        <h2 class="section-title">实用工具</h2>
        <div class="tools-grid">
            <div class="tool-card">
                <img src="{{ url_for('static', filename='img/calculator.svg') }}" alt="预产期计算">
                <h3>预产期计算器</h3>
                <p>根据末次月经时间，准确计算预产期</p>
                <a href="{{ url_for('main.tools', tool='due_date') }}" class="btn btn-outline-primary">立即使用</a>
            </div>
            <div class="tool-card">
                <img src="{{ url_for('static', filename='img/ovulation.svg') }}" alt="排卵期计算">
                <h3>排卵期计算器</h3>
                <p>帮助您找到最佳受孕时间</p>
                <a href="{{ url_for('main.tools', tool='ovulation') }}" class="btn btn-outline-primary">立即使用</a>
            </div>
            <div class="tool-card">
                <img src="{{ url_for('static', filename='img/weight.svg') }}" alt="BMI计算">
                <h3>BMI计算器</h3>
                <p>监测孕期体重变化</p>
                <a href="{{ url_for('main.tools', tool='bmi') }}" class="btn btn-outline-primary">立即使用</a>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
.hero-section {
    background-color: #f8f9fa;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 600px;
    position: relative;
    z-index: 2;
}

.hero-image {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    max-width: 50%;
    z-index: 1;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.article-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
}

.article-card.featured {
    grid-column: span 2;
}

.experts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.tool-card {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.medical-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(25, 135, 84, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8em;
}

@media (max-width: 768px) {
    .hero-image {
        position: relative;
        max-width: 100%;
        margin-top: 30px;
        transform: none;
    }
    
    .article-card.featured {
        grid-column: span 1;
    }
}
</style>
{% endblock %} 