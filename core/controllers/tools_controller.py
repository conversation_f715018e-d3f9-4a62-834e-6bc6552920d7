#!/usr/bin/env python3
"""
工具控制器 - 处理各种计算器和实用工具
"""

from flask import Blueprint, render_template, request, jsonify
from datetime import datetime, timedelta
import json
import math

# 创建工具蓝图
tools = Blueprint('tools', __name__)

@tools.route('/tools')
def tools_index():
    """工具首页 - 显示所有可用工具"""
    tools_list = [
        {
            'name': '预产期计算器',
            'slug': 'due-date-calculator',
            'description': '根据末次月经日期计算预产期和当前孕周',
            'icon': 'fas fa-calendar-alt',
            'category': 'pregnancy',
            'image': '/static/img/tools/due-date.svg'
        },
        {
            'name': '生长发育计算器',
            'slug': 'growth-calculator',
            'description': '根据WHO标准评估宝宝的身高、体重和头围',
            'icon': 'fas fa-chart-line',
            'category': 'baby',
            'image': '/static/img/tools/growth.svg'
        },
        {
            'name': '受孕日期计算器',
            'slug': 'conception-calculator',
            'description': '根据预产期或末次月经推算受孕日期',
            'icon': 'fas fa-heart',
            'category': 'pregnancy',
            'image': '/static/img/tools/conception.svg'
        },
        {
            'name': '孕期体重计算器',
            'slug': 'pregnancy-weight-calculator',
            'description': '根据孕前BMI计算孕期体重增长范围',
            'icon': 'fas fa-weight',
            'category': 'pregnancy',
            'image': '/static/img/tools/weight.svg'
        },
        {
            'name': '婴儿花费计算器',
            'slug': 'baby-cost-calculator',
            'description': '估算婴儿第一年的各项花费',
            'icon': 'fas fa-dollar-sign',
            'category': 'planning',
            'image': '/static/img/tools/cost.svg'
        },
        {
            'name': '儿童身高预测器',
            'slug': 'height-predictor',
            'description': '基于父母身高预测孩子成年身高',
            'icon': 'fas fa-ruler-vertical',
            'category': 'baby',
            'image': '/static/img/tools/height.svg'
        },
        {
            'name': '宝宝性别预测器',
            'slug': 'gender-predictor',
            'description': '基于传统方法预测宝宝性别（仅供娱乐）',
            'icon': 'fas fa-baby',
            'category': 'fun',
            'image': '/static/img/tools/gender.svg'
        }
    ]
    
    return render_template('tools/index.html', tools=tools_list)

@tools.route('/tools/due-date-calculator')
def due_date_calculator():
    """预产期计算器页面"""
    return render_template('tools/due_date_calculator.html')

@tools.route('/tools/growth-calculator')
def growth_calculator():
    """生长发育计算器页面"""
    return render_template('tools/growth_calculator.html')

@tools.route('/tools/conception-calculator')
def conception_calculator():
    """受孕日期计算器页面"""
    return render_template('tools/conception_calculator.html')

@tools.route('/tools/pregnancy-weight-calculator')
def pregnancy_weight_calculator():
    """孕期体重计算器页面"""
    return render_template('tools/pregnancy_weight_calculator.html')

@tools.route('/tools/baby-cost-calculator')
def baby_cost_calculator():
    """婴儿花费计算器页面"""
    return render_template('tools/baby_cost_calculator.html')

@tools.route('/tools/height-predictor')
def height_predictor():
    """儿童身高预测器页面"""
    return render_template('tools/height_predictor.html')

@tools.route('/tools/gender-predictor')
def gender_predictor():
    """宝宝性别预测器页面"""
    return render_template('tools/gender_predictor.html')

# API 路由 - 计算功能
@tools.route('/api/calculate-due-date', methods=['POST'])
def api_calculate_due_date():
    """预产期计算API"""
    try:
        data = request.get_json()
        last_period_str = data.get('last_period')
        cycle_length = int(data.get('cycle_length', 28))
        
        # 解析日期
        last_period = datetime.strptime(last_period_str, '%Y-%m-%d')
        
        # 计算预产期 (内格尔规则: 末次月经 + 280天)
        due_date = last_period + timedelta(days=280)
        
        # 计算当前孕周
        today = datetime.now()
        days_pregnant = (today - last_period).days
        weeks_pregnant = days_pregnant // 7
        days_remainder = days_pregnant % 7
        
        # 计算孕期阶段
        first_trimester_end = last_period + timedelta(days=91)  # 13周
        second_trimester_end = last_period + timedelta(days=182)  # 26周
        
        result = {
            'due_date': due_date.strftime('%Y-%m-%d'),
            'due_date_formatted': due_date.strftime('%Y年%m月%d日'),
            'weeks_pregnant': weeks_pregnant,
            'days_remainder': days_remainder,
            'days_pregnant': days_pregnant,
            'first_trimester': {
                'start': last_period.strftime('%Y-%m-%d'),
                'end': first_trimester_end.strftime('%Y-%m-%d')
            },
            'second_trimester': {
                'start': (first_trimester_end + timedelta(days=1)).strftime('%Y-%m-%d'),
                'end': second_trimester_end.strftime('%Y-%m-%d')
            },
            'third_trimester': {
                'start': (second_trimester_end + timedelta(days=1)).strftime('%Y-%m-%d'),
                'end': (due_date - timedelta(days=1)).strftime('%Y-%m-%d')
            }
        }
        
        return jsonify({'success': True, 'result': result})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@tools.route('/api/calculate-conception', methods=['POST'])
def api_calculate_conception():
    """受孕日期计算API"""
    try:
        data = request.get_json()
        calculation_type = data.get('type')  # 'from_lmp' 或 'from_due_date'
        
        if calculation_type == 'from_lmp':
            last_period_str = data.get('last_period')
            cycle_length = int(data.get('cycle_length', 28))
            
            last_period = datetime.strptime(last_period_str, '%Y-%m-%d')
            ovulation_date = last_period + timedelta(days=cycle_length - 14)
            conception_date = ovulation_date  # 受孕通常在排卵日
            
        else:  # from_due_date
            due_date_str = data.get('due_date')
            due_date = datetime.strptime(due_date_str, '%Y-%m-%d')
            conception_date = due_date - timedelta(days=266)  # 平均孕期266天
            ovulation_date = conception_date
        
        # 计算受孕窗口期 (排卵前5天到排卵后1天)
        fertile_window_start = ovulation_date - timedelta(days=5)
        fertile_window_end = ovulation_date + timedelta(days=1)
        
        result = {
            'conception_date': conception_date.strftime('%Y-%m-%d'),
            'conception_date_formatted': conception_date.strftime('%Y年%m月%d日'),
            'ovulation_date': ovulation_date.strftime('%Y-%m-%d'),
            'ovulation_date_formatted': ovulation_date.strftime('%Y年%m月%d日'),
            'fertile_window': {
                'start': fertile_window_start.strftime('%Y-%m-%d'),
                'end': fertile_window_end.strftime('%Y-%m-%d'),
                'start_formatted': fertile_window_start.strftime('%Y年%m月%d日'),
                'end_formatted': fertile_window_end.strftime('%Y年%m月%d日')
            }
        }
        
        return jsonify({'success': True, 'result': result})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@tools.route('/api/calculate-pregnancy-weight', methods=['POST'])
def api_calculate_pregnancy_weight():
    """孕期体重计算API"""
    try:
        data = request.get_json()
        pre_pregnancy_weight = float(data.get('pre_pregnancy_weight'))
        height = float(data.get('height'))  # 厘米
        current_weight = float(data.get('current_weight', 0))
        weeks_pregnant = int(data.get('weeks_pregnant', 0))
        
        # 计算BMI
        height_m = height / 100
        bmi = pre_pregnancy_weight / (height_m ** 2)
        
        # 根据BMI确定体重增长建议
        if bmi < 18.5:
            category = '体重过轻'
            total_gain_min = 12.5
            total_gain_max = 18
        elif bmi < 25:
            category = '正常体重'
            total_gain_min = 11.5
            total_gain_max = 16
        elif bmi < 30:
            category = '超重'
            total_gain_min = 7
            total_gain_max = 11.5
        else:
            category = '肥胖'
            total_gain_min = 5
            total_gain_max = 9
        
        # 计算当前应该增重范围
        if weeks_pregnant <= 12:
            # 前12周建议增重1-2公斤
            current_gain_min = 1
            current_gain_max = 2
        else:
            # 12周后按比例计算
            remaining_weeks = 40 - 12
            current_weeks = weeks_pregnant - 12
            progress = current_weeks / remaining_weeks
            
            current_gain_min = 1 + (total_gain_min - 1) * progress
            current_gain_max = 2 + (total_gain_max - 2) * progress
        
        # 计算实际增重
        actual_gain = current_weight - pre_pregnancy_weight if current_weight > 0 else 0
        
        result = {
            'bmi': round(bmi, 1),
            'bmi_category': category,
            'total_gain_range': {
                'min': total_gain_min,
                'max': total_gain_max
            },
            'current_gain_range': {
                'min': round(current_gain_min, 1),
                'max': round(current_gain_max, 1)
            },
            'actual_gain': round(actual_gain, 1),
            'is_within_range': current_gain_min <= actual_gain <= current_gain_max if current_weight > 0 else None
        }
        
        return jsonify({'success': True, 'result': result})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400
