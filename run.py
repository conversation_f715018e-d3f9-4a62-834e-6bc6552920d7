#!/usr/bin/env python3
import sys
import os
import logging

# 使用更可靠的方式添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 从各个模块导入必要的组件
from flask import Flask, render_template, redirect, url_for, flash
from flask_login import LoginManager, current_user
from flask_migrate import Migrate
import secrets
from datetime import datetime

# 导入模型和蓝图
from core.models import db, User, Article, Category, Tag, Comment
from api import api
from auth import auth
from core.main import main
from core.admin import admin
from core.controllers.tools_controller import tools

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_app(config=None):
    app = Flask(__name__,
               static_folder='static',
               template_folder='templates')
    
    # 使用新的数据库路径
    instance_path = os.path.join(current_dir, 'instance')
    if not os.path.exists(instance_path):
        os.makedirs(instance_path)
        logger.info(f"创建实例目录: {instance_path}")
    
    # 设置绝对路径的数据库URI
    db_path = os.path.join(instance_path, 'articles.db')
    logger.info(f"数据库路径: {db_path}")
    
    # 确保上传目录存在
    uploads_path = os.path.join(current_dir, 'static', 'uploads')
    if not os.path.exists(uploads_path):
        os.makedirs(uploads_path)
        logger.info(f"创建上传目录: {uploads_path}")
    
    # 配置
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or secrets.token_hex(16)
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['UPLOAD_FOLDER'] = uploads_path
    app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx'}
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
    app.config['ARTICLES_PER_PAGE'] = 10  # 每页显示10篇文章
    
    logger.info(f"SQLAlchemy URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
    
    # 应用自定义配置
    if config:
        app.config.update(config)
    
    # 注册Jinja2模板过滤器
    from utils.utils import date_filter, datetime_filter
    app.jinja_env.filters['date'] = date_filter
    app.jinja_env.filters['datetime'] = datetime_filter
    
    # 初始化数据库
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # 注册蓝图
    app.register_blueprint(api, url_prefix='/api')
    app.register_blueprint(auth)
    app.register_blueprint(main)
    app.register_blueprint(admin, url_prefix='/admin')
    app.register_blueprint(tools)
    
    # 设置登录管理器
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # 使用已存在的数据库，不再创建新表
    with app.app_context():
        logger.info("使用现有数据库，不创建新表")
        # 不再调用db.create_all()
        # 也不再创建默认管理员
    
    return app

if __name__ == '__main__':
    logger.info("启动应用程序...")
    app = create_app()
    app.run(debug=True, port=5002, threaded=True) 